const kStorageToken = 'token-storage';
const kStorageRefreshToken = 'refresh-token-storage';

const kStorageIsBiometricActive = 'biometric-status';
const kStorageUsername = 'username-encrypred';
const kStoragePassword = 'password-encrypted';

const kStorageUserId = 'user-id';
const kStorageAgentCode = 'agent-code';
const kStorageAgentName = 'agent-name';
const kStorageAgentBranch = 'agent-branch';
const kStorageUserFirestoreId = 'user-firestore-id';
const kStorageUserLevel = "user-level";
const kStorageUserLevelName = "user-level-name";
const kStorageUserLevelComplete = "user-level-complete";

const kDeviceId = 'device-id';

// approval status can edit
const kApprovalStatusApprove = 'DISETUJUI';
const kApprovalStatusReject = 'DITOLAK';
const kApprovalStatusCanceled = 'DIBATALKAN';
// approvl status cannot edit
const kApprovalStatusNew = 'BARU';
const kApprovalStatusPending = 'TERTUNDA';
const kApprovalStatusWaiting = 'MENUNGGU_PERSETUJUAN';

// list of field
const kFieldleaderCode = 'leaderCode';
const kFieldagentCode = 'agentCode';
const kFieldagentName = 'agentName';
const kFielddistributionCode = 'distributionCode';
const kFieldroleName = 'roleName';
const kFieldlevel = 'level';
const kFieldpositionLevel = 'positionLevel';
const kFieldregionCode = 'regionCode';
const kFieldregionName = 'regionName';
const kFieldsubRegionCode = 'subRegionCode';
const kFieldsubRegionName = 'subRegionName';
const kFieldareaCode = 'areaCode';
const kFieldareaName = 'areaName';
const kFieldbranchCode = 'branchCode';
const kFieldbranchName = 'branchName';
const kFieldgroupCode = 'groupCode';
const kFieldgroupName = 'groupName';
const kFieldlicenseNumberAAJI = 'licenseNumberAAJI';
const kFieldlicenseExpiredDateAAJI = 'licenseExpiredDateAAJI';
const kFieldlicenseNumberAASI = 'licenseNumberAASI';
const kFieldlicenseExpiredDateAASI = 'licenseExpiredDateAASI';
const kFieldleaderG2G = 'leaderG2G';
const kFieldrecruiterCode = 'recruiterCode';
const kFielddob = 'dob';
const kFieldgender = 'gender';
const kFieldeducation = 'education';
const kFieldstatus = 'status';
const kFieldchannel = 'channel';
const kFieldemail = 'email';
const kFieldphoneNumber = 'phoneNumber';
const kFieldaddress = 'address';
const kFieldbankAccountNumber = 'bankAccountNumber';
const kFieldbank = 'bank';
const kFieldmaritalStatus = 'maritalStatus';
const kFieldbankAttachment = 'bankAttachment';
const kFieldktpAttachment = 'ktpAttachment';
const kFieldkkAttachment = 'kkAttachment';
const kFieldphoto = 'photo';
const kFieldcreatedAt = 'createdAt';
const kFieldupdatedAt = 'updatedAt';
const kFieldmbranchCode = 'mbranchCode';
const kFieldmbranchName = 'mbranchName';
const kFieldsbranchCode = 'sbranchCode';
const kFieldsbranchName = 'sbranchName';

const kInfoTypeTnc = 'PRIVACY_POLICY';
const kInfoTypeAboutUs = 'ABOUT_US';
const kInfoTypeMemo = 'MEMO';
const kInfoTypeFormCas = 'FORM_CAS';
const kInfoTypeFormOps = 'FORM_OPS';
const kInfoTypeSuratEdaran = 'SURAT_EDARAN';
const kInfoTypeNews = 'NEWS';
const kInfoTypeTutorial = 'TUTORIAL';
const kInfoTypeFingerPrint = 'FINGERPRINT';

// requestCode
const kReqLogin = 1;
const kReqAddDevices = 2;
const kReqGetDevices = 3;
const kReqRevokeDevices = 4;
const kReqUploadProfile = 5;
const kReqGetCurrentUpdateProfile = 6;
const kReqGetFlyer = 7;
const kReqGetInfo = 8;
const kReqGetProfile = 9;
// Widget Produksi Saya summary
const kReqGetWgtSumMonth = 10;
const kReqGetWgtSumYear = 11;
// Widget Produksi saya Page
const kReqWidgetProductionMonth = 12;
const kReqWidgetProductionYear = 13;
const kReqWidgetProductionTeamMonth = 20;
const kReqWidgetProductionTeamYear = 21;
const kReqGetPromosiAgent = 14;
const kReqGetValidasiHirarki = 15;
const kReqGetCommission = 16;
const kReqGetDetailCommission = 17;
const kReqGetPersistencyIndividu = 18;
const kReqGetPersistencyTeam = 19;
const kReqGetProductionGraphicMonth = 22;
const kReqGetProductionGraphicYear = 23;
// ComboBox
const kReqGetComboBoxLevel = 24;
const kReqGetComboBoxBank = 25;
const kReqGetBranch = 26;
const kReqGetComboBoxOccupation = 27;
// Recruitment
const kReqSaveDraft = 28;
const kReqGetRecruitmentList = 29;
const kReqPostRequestVerif = 30;
const kReqGetIsVerified = 31;

// firestore
const kFireCollectionAgent = "agents";
const kFireDocTheme = "theme_is_dark";
const kFireDocLanguage = "language";

// UserLEvel
const kUserLevelBp = 'ROLE_AGE_BP';
const kUserLevelBm = 'ROLE_AGE_BM';
const kUserLevelBd = 'ROLE_AGE_BD';
const kUserLevelBdm = 'ROLE_AGE_BDM';
const kUserLevelBDD = 'ROLE_AGE_BDD';
const kUserLevelABDD = 'ROLE_AGE_ABDD';
const kUserLevelHOS = 'ROLE_AGE_HOS';
const kUserLevelCAO = 'ROLE_AGE_CAO';

// others
const kLevelBP = 'BP';
const kLevelBM = 'BM';
const kLevelBD = 'BD';

// switch yearly
const kSwitchYearly = 'select-year';
const kSwitchMonthly = 'select-month';
// Switch team
const kSwitchProdTeam = 'select-team';
const kSwitchProdIndividu = 'select-individu';
// Switch for other user levels
const kSwitchProdGroup = 'select-group';
const kSwitchProdBranch = 'select-branch';
const kSwitchProdArea = 'select-area';

// Routes Arguments
const kArgsUserLevel = 'user-level';
const kArgsProductionType = 'production-type';
const kArgsProductionTeamType = 'production-team-type';

// SPAJ Status
const kSpajStatPendAdmin = 'PENDING ADMIN';
const kSpajStatPendUw = 'PENDING UW';
const kSpajStatPendCheck = 'PENDING CHECKER';
const kSpajStatAccept = 'ACCEPT';
const kSpajStatReject = 'REJECT';
const kSpajStatPolicy = 'POLICY';

// Widget Keys
const kWidgetKeyUlangTahunNasabah = 'ulangTahunNasabah';
const kWidgetKeyStatusKlaim = 'statusKlaim';
const kWidgetKeyPolisLapse = 'polisLapse';
const kWidgetKeyPolisJatuhTempo = 'polisJatuhTempo';
const kWidgetKeyProduksiSaya = 'produksiSaya';
const kWidgetKeyValidasiPromosi = 'validasiPromosi';
const kWidgetKeyEstimasiKompensasi = 'estimasiKompensasi';
const kWidgetKeyPersistensi = 'persistensi';
const kWidgetKeyStatusSpaj = 'statusSpaj';

// Photo Type
const kPhotoTypeKtp = 'ktp';
const kPhotoTypeSelfieKtp = 'selfie-ktp';
const kPhotoTypePasFoto = 'pas-foto';
